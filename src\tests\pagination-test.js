/**
 * Pagination Test Script
 * 
 * Tests the new pagination implementation to ensure:
 * 1. No duplicate posts in infinite scroll
 * 2. Proper pagination with LIMIT/OFFSET
 * 3. Correct hasMore logic
 * 4. Backward compatibility with existing functionality
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { usePostsStore } from '../stores/posts/index.js'

// Mock Supabase for testing
const mockSupabase = {
  from: () => ({
    select: () => ({
      order: () => ({
        range: (start, end) => ({
          then: (callback) => {
            // Simulate paginated data
            const allPosts = Array.from({ length: 50 }, (_, i) => ({
              id: i + 1,
              title: `Test Post ${i + 1}`,
              content: `Content for post ${i + 1}`,
              created_at: new Date(Date.now() - i * 1000 * 60 * 60).toISOString(),
              status: 'published',
              post_type: 'platform',
              sub_type: 'general',
              user_id: 'test-user',
              comments_count: 0,
              likes_count: 0
            }))
            
            const pageData = allPosts.slice(start, end + 1)
            callback({ data: pageData, error: null })
            return Promise.resolve({ data: pageData, error: null })
          }
        })
      })
    })
  })
}

// Test pagination functionality
async function testPagination() {
  console.log('🧪 Testing Pagination Implementation...')
  
  const app = createApp({})
  const pinia = createPinia()
  app.use(pinia)
  
  const postsStore = usePostsStore()
  
  // Test 1: Initial load (page 1)
  console.log('\n📄 Test 1: Initial load (page 1)')
  await postsStore.fetchPosts({ page: 1, limit: 10, paginationMode: 'replace' })
  console.log(`✅ Loaded ${postsStore.posts.length} posts`)
  console.log(`✅ hasMorePosts: ${postsStore.hasMorePosts}`)
  
  const firstPageIds = postsStore.posts.map(p => p.id)
  console.log(`✅ First page IDs: [${firstPageIds.join(', ')}]`)
  
  // Test 2: Load more (page 2) - should append
  console.log('\n📄 Test 2: Load more (page 2)')
  await postsStore.fetchPosts({ page: 2, limit: 10, paginationMode: 'append' })
  console.log(`✅ Total posts after page 2: ${postsStore.posts.length}`)
  
  const allIds = postsStore.posts.map(p => p.id)
  const uniqueIds = [...new Set(allIds)]
  
  if (allIds.length === uniqueIds.length) {
    console.log('✅ No duplicate posts found')
  } else {
    console.log('❌ Duplicate posts detected!')
    console.log(`Total: ${allIds.length}, Unique: ${uniqueIds.length}`)
  }
  
  // Test 3: fetchMorePosts method
  console.log('\n📄 Test 3: fetchMorePosts method')
  const beforeCount = postsStore.posts.length
  await postsStore.fetchMorePosts({ limit: 10 })
  const afterCount = postsStore.posts.length
  
  console.log(`✅ Posts before: ${beforeCount}, after: ${afterCount}`)
  console.log(`✅ Added ${afterCount - beforeCount} new posts`)
  
  // Test 4: Backward compatibility - no pagination params
  console.log('\n📄 Test 4: Backward compatibility')
  await postsStore.fetchPosts() // Should work without pagination params
  console.log(`✅ Backward compatibility test passed`)
  
  console.log('\n🎉 All pagination tests completed!')
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  window.testPagination = testPagination
  console.log('Pagination test loaded. Run window.testPagination() to execute.')
} else {
  // Node environment
  testPagination().catch(console.error)
}

export { testPagination }
