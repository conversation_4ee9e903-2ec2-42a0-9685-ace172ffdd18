import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { supabase } from '@/lib/supabase';
import {
  Post,
  PostFilter,
  PostWithAuthor,
  mapPostFromDatabase
} from '@/types/post';
import { useStorageService } from '@/services/storageService';
import { useNotificationStore } from '@/stores/notifications';
import { useGlobalServicesStore } from '@/stores/globalServices';
import { sanitizeFormInput } from '@/lib/security';
import { useCommentsService } from '@/services/commentsService';

export const usePostsStore = defineStore('posts', () => {
  // State
  const posts = ref<Post[]>([]);
  const userPosts = ref<Post[]>([]);
  const loading = ref(false);
  const error = ref<string | null>(null);
  const currentFilter = ref<PostFilter>({
    searchQuery: '',
    postTypes: [],
    subTypes: [],
    dateRange: 'all',
    tags: []
  });

  // Pagination state
  const nextCursor = ref<string | null>(null);
  const hasMorePosts = ref(true);

  // Getters
  const filteredPosts = computed(() => {
    let result = [...posts.value];

    // Apply search filter
    if (currentFilter.value.searchQuery) {
      const query = currentFilter.value.searchQuery.toLowerCase();
      result = result.filter(post =>
        (post.title && post.title.toLowerCase().includes(query)) ||
        (post.content && post.content.toLowerCase().includes(query)) ||
        (post.author && post.author.toLowerCase().includes(query)) ||
        (post.blogTitle && post.blogTitle.toLowerCase().includes(query)) ||
        (post.eventTitle && post.eventTitle.toLowerCase().includes(query))
      );
    }

    // Apply post type filter
    if (currentFilter.value.postTypes && currentFilter.value.postTypes.length > 0) {
      result = result.filter(post =>
        post.postType && currentFilter.value.postTypes.includes(post.postType)
      );
    }

    // Apply sub type filter
    if (currentFilter.value.subTypes && currentFilter.value.subTypes.length > 0) {
      result = result.filter(post =>
        post.subType && currentFilter.value.subTypes.includes(post.subType)
      );
    }

    // Apply date range filter
    if (currentFilter.value.dateRange && currentFilter.value.dateRange !== 'all') {
      const now = new Date();
      let cutoffDate = new Date();

      switch (currentFilter.value.dateRange) {
        case 'today':
          cutoffDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          cutoffDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          cutoffDate.setMonth(now.getMonth() - 1);
          break;
      }

      result = result.filter(post => {
        const postDate = new Date(post.createdAt || '');
        return postDate >= cutoffDate;
      });
    }

    // Apply tag filter
    if (currentFilter.value.tags && currentFilter.value.tags.length > 0) {
      result = result.filter(post =>
        post.tags && post.tags.some(tag =>
          currentFilter.value.tags?.includes(tag.toLowerCase())
        )
      );
    }

    return result;
  });

  // Helper function to refresh a single post from database
  async function refreshSinglePost(postId: number): Promise<void> {
    try {
      const { data: updatedPostData, error: fetchError } = await supabase
        .from('posts_with_authors')
        .select('*')
        .eq('id', postId)
        .single();

      if (fetchError) {
        console.error('refreshSinglePost: Error fetching updated post data:', fetchError);
        throw fetchError;
      }

      // Update the post in local state with fresh database data
      const updatedPost = mapPostFromDatabase(updatedPostData);
      const postIndex = posts.value.findIndex(p => p.id === postId);
      if (postIndex !== -1) {
        posts.value[postIndex] = updatedPost;
        console.log('refreshSinglePost: Updated post in local state:', {
          id: updatedPost.id,
          likesCount: updatedPost.likesCount,
          commentsCount: updatedPost.commentsCount,
          isLiked: updatedPost.isLiked
        });
      } else {
        console.warn('refreshSinglePost: Post not found in local state for update');
      }
    } catch (error) {
      console.error('refreshSinglePost: Failed to refresh post:', error);
      throw error;
    }
  }

  // Actions
  async function fetchPosts(filter?: PostFilter) {
    console.log('fetchPosts called with filter:', filter);
    loading.value = true;
    error.value = null;

    try {
      if (filter) {
        currentFilter.value = { ...currentFilter.value, ...filter };
      }

      console.log('Current filter after merge:', currentFilter.value);

      // Use the posts_with_authors view to get posts with author and like information
      let query = supabase
        .from('posts_with_authors')
        .select('*')
        .order('created_at', { ascending: false });

      console.log('Base query created');

      // Handle pagination if specified
      const isPaginated = filter?.page !== undefined || filter?.limit !== undefined;
      const paginationMode = filter?.paginationMode || 'replace';
      const page = filter?.page || 1;
      const limit = filter?.limit || (isPaginated ? 10 : undefined);

      console.log('Pagination settings:', { isPaginated, paginationMode, page, limit });

      // Apply filters if provided
      if (currentFilter.value.searchQuery) {
        query = query.or(`content.ilike.%${currentFilter.value.searchQuery}%,title.ilike.%${currentFilter.value.searchQuery}%,blog_title.ilike.%${currentFilter.value.searchQuery}%,event_title.ilike.%${currentFilter.value.searchQuery}%`);
        console.log('Applied search query filter:', currentFilter.value.searchQuery);
      }

      // We're primarily filtering by sub_type since that's how posts are categorized in the database
      if (currentFilter.value.subTypes && currentFilter.value.subTypes.length > 0) {
        query = query.in('sub_type', currentFilter.value.subTypes);
        console.log('Applied sub_type filter:', currentFilter.value.subTypes);
      }

      // Only use post_type filtering if specifically needed
      if (currentFilter.value.postTypes && currentFilter.value.postTypes.length > 0) {
        // Most posts have post_type='platform', so we don't need to filter by it
        // unless we're looking for specific post_types like 'admin'
        if (currentFilter.value.postTypes.includes('admin')) {
          query = query.in('post_type', currentFilter.value.postTypes);
          console.log('Applied post_type filter:', currentFilter.value.postTypes);
        }
      }

      if (currentFilter.value.tags && currentFilter.value.tags.length > 0) {
        // For array columns, we need a different approach
        currentFilter.value.tags.forEach(tag => {
          query = query.contains('tags', [tag]);
        });
        console.log('Applied tags filter:', currentFilter.value.tags);
      }

      if (currentFilter.value.dateRange && currentFilter.value.dateRange !== 'all') {
        const now = new Date();
        let startDate = new Date();

        switch (currentFilter.value.dateRange) {
          case 'today':
            startDate.setHours(0, 0, 0, 0);
            break;
          case 'week':
            startDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            startDate.setMonth(now.getMonth() - 1);
            break;
        }

        query = query.gte('created_at', startDate.toISOString());
        console.log('Applied date range filter:', currentFilter.value.dateRange, 'from:', startDate.toISOString());
      }

      console.log('Executing query...');

      // Apply pagination if specified
      if (isPaginated && limit) {
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit - 1;
        query = query.range(startIndex, endIndex);
        console.log('Applied pagination:', { startIndex, endIndex, page, limit });
      }

      // Execute the query
      const { data, error: err } = await query;

      if (err) {
        console.error('Supabase query error:', err);
        throw err;
      }

      console.log('Query executed successfully, data length:', data?.length || 0);

      if (data && data.length > 0) {
        // Map the database fields to our frontend model
        const mappedPosts = data.map((post: PostWithAuthor) => mapPostFromDatabase(post));

        // Handle pagination mode
        if (isPaginated && paginationMode === 'append' && page > 1) {
          // For infinite scroll: append new posts and remove duplicates
          const existingIds = new Set(posts.value.map(p => p.id));
          const newPosts = mappedPosts.filter(post => !existingIds.has(post.id));
          posts.value = [...posts.value, ...newPosts];
          console.log('Appended new posts, total count:', posts.value.length, 'new posts:', newPosts.length);
        } else {
          // For normal fetch: replace posts
          posts.value = mappedPosts;
          console.log('Replaced posts, count:', posts.value.length);
        }

        // Update pagination state
        if (isPaginated && limit) {
          hasMorePosts.value = data.length >= limit;
          console.log('Updated hasMorePosts:', hasMorePosts.value);
        }
      } else {
        if (!isPaginated || paginationMode === 'replace') {
          posts.value = [];
        }
        hasMorePosts.value = false;
        console.log('No posts found');
      }

      return filteredPosts.value;
    } catch (err: any) {
      console.error('Error fetching posts:', err);
      error.value = err.message || 'Failed to fetch posts';
      return [];
    } finally {
      loading.value = false;
      console.log('fetchPosts completed, loading set to false');
    }
  }

  // Dedicated method for infinite scroll pagination
  async function fetchMorePosts(filter?: Omit<PostFilter, 'paginationMode'>) {
    console.log('fetchMorePosts called with filter:', filter);

    // Calculate next page based on current posts count and limit
    const limit = filter?.limit || 10;
    const currentPage = Math.floor(posts.value.length / limit) + 1;

    return await fetchPosts({
      ...filter,
      page: currentPage,
      limit,
      paginationMode: 'append'
    });
  }

  async function likePost(postId: number) {
    try {
      console.log('likePost: Starting like operation for post:', postId);

      // Initialize activity tracking service
      const globalServices = useGlobalServicesStore();
      const activityService = globalServices.activityService;

      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        console.error('likePost: User not authenticated');
        throw new Error('User not authenticated');
      }

      const post = posts.value.find(p => p.id === postId);
      if (!post) {
        console.error('likePost: Post not found in local state:', postId);
        throw new Error('Post not found');
      }

      console.log('likePost: Current post state:', {
        id: post.id,
        likesCount: post.likesCount,
        isLiked: post.isLiked
      });

      // Check if the user has already liked the post
      const { data: existingLike, error: likeCheckError } = await supabase
        .from('likes')
        .select('*')
        .eq('post_id', postId)
        .eq('user_id', userId)
        .single();

      if (likeCheckError && likeCheckError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        throw likeCheckError;
      }

      if (existingLike) {
        console.log('likePost: User already liked post, unliking...');

        // User already liked the post, so unlike it
        const { error: unlikeError } = await supabase
          .from('likes')
          .delete()
          .eq('id', existingLike.id);

        if (unlikeError) {
          console.error('likePost: Error deleting like:', unlikeError);
          throw unlikeError;
        }

        console.log('likePost: Successfully unliked post');

      } else {
        console.log('likePost: User hasn\'t liked post yet, liking...');

        // User hasn't liked the post yet, so like it
        const { error: likeError } = await supabase
          .from('likes')
          .insert({ post_id: postId, user_id: userId });

        if (likeError) {
          console.error('likePost: Error inserting like:', likeError);
          throw likeError;
        }

        console.log('likePost: Successfully liked post');

        // Track the activity (only when liking, not unliking)
        try {
          await activityService.trackActivity('post_like', {
            post_id: postId,
            post_title: post.title || post.blogTitle || post.eventTitle || '',
            author_id: post.userId,
            author_name: post.author || ''
          });
        } catch (activityError) {
          console.warn('likePost: Error tracking activity (non-critical):', activityError);
          // Don't throw here as the like operation was successful
        }
      }

      // Refresh the specific post from database to get updated like state and count
      console.log('likePost: Refreshing post data from database...');
      await refreshSinglePost(postId);

      console.log('likePost: Like operation completed successfully');
      const updatedPost = posts.value.find(p => p.id === postId);
      return updatedPost;
    } catch (err: any) {
      console.error('likePost: Error in like operation:', err);
      error.value = err.message || 'Failed to like post';
      return null;
    }
  }

  async function createPost(newPost: Partial<Post>) {
    loading.value = true;
    error.value = null;

    // Initialize activity tracking service
    const globalServices = useGlobalServicesStore();
    const activityService = globalServices.activityService;
    // Initialize storage service
    const storageService = useStorageService();

    // Set the database post_type based on the source
    // For posts created from the frontend by regular users, use 'platform'
    // The database constraint only allows 'automated', 'admin', or 'platform'
    const dbPostType = 'platform'; // Use 'platform' for posts created from the frontend

    // Keep track of the original post type for frontend display
    const originalPostType = (newPost.postType || 'general').toLowerCase();

    try {
      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Handle image upload if present
      let imageUrl = null;
      try {
        if (newPost.featuredImage) {
          // Check if the image is a data URL (base64)
          if (typeof newPost.featuredImage === 'string' && newPost.featuredImage.startsWith('data:')) {
            // Explicitly use 'imagefiles' bucket
            imageUrl = await storageService.uploadDataUrl(
              newPost.featuredImage,
              'imagefiles',
              originalPostType
            );

            // Ensure the URL is properly formatted with the full Supabase path
            if (imageUrl && !imageUrl.startsWith('http')) {
              // If it's not a full URL, construct the Supabase storage URL
              imageUrl = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${imageUrl}`;
            }
          } else {
            // If it's already a URL, use it directly
            imageUrl = newPost.featuredImage;
          }
        } else if (newPost.image && typeof newPost.image === 'string') {
          // Handle image from the image property if featuredImage is not present
          if (newPost.image.startsWith('data:')) {
            // Explicitly use 'imagefiles' bucket
            imageUrl = await storageService.uploadDataUrl(
              newPost.image,
              'imagefiles',
              originalPostType
            );

            // Ensure the URL is properly formatted with the full Supabase path
            if (imageUrl && !imageUrl.startsWith('http')) {
              // If it's not a full URL, construct the Supabase storage URL
              imageUrl = `https://dpicnvisvxpmgjtbeicf.supabase.co/storage/v1/object/public/${imageUrl}`;
            }
          } else {
            imageUrl = newPost.image;
          }
        }
      } catch (uploadError) {
        console.error('Error uploading image:', uploadError);
        // Continue without the image rather than failing the whole post creation
        imageUrl = null;
        // Show a warning to the user
        const notificationStore = useNotificationStore();
        notificationStore.warning('Could not upload image, but your post will still be created.');
      }

      // Prepare the post data based on the actual database schema
      // Make sure to include all required fields according to the database schema
      const postData: any = {
        user_id: userId,
        post_type: dbPostType,
        content: newPost.content || '', // Required field
        title: newPost.title || null,
        excerpt: newPost.excerpt || null,
        featured_image: imageUrl, // Use featured_image instead of image_url
        // Always use blog_category instead of category since there's no category column
        blog_category: (newPost as any).blog_category || newPost.blogCategory || newPost.category || null,
        sub_type: newPost.subType || originalPostType, // Use sub_type instead of sub_category
        tags: newPost.tags || [],
        status: newPost.status || 'published', // Required field
        // likes_count and comments_count will be set by database defaults
        slug: newPost.slug || null
      };

      // Handle media_urls for additional images - must be a JSONB object in the database
      if (newPost.mediaUrls && Array.isArray(newPost.mediaUrls) && newPost.mediaUrls.length > 0) {
        // Convert array to JSONB object with indices as keys
        const mediaUrlsObj = {};
        newPost.mediaUrls.forEach((url, index) => {
          mediaUrlsObj[index.toString()] = url;
        });
        postData.media_urls = mediaUrlsObj;
      } else if ((newPost as any).images && Array.isArray((newPost as any).images) && (newPost as any).images.length > 1) {
        // If we have multiple images in the images array, use them for media_urls
        const mediaUrlsObj = {};
        (newPost as any).images.slice(1).forEach((url: string, index: number) => {
          mediaUrlsObj[index.toString()] = url;
        });
        postData.media_urls = mediaUrlsObj;
      } else {
        postData.media_urls = {};
      }

      // Add specialized fields based on post type
      switch (originalPostType) {
        case 'blog':
          postData.title = newPost.blogTitle || newPost.title || 'Blog Post';
          // Handle both blog_category and category fields for compatibility
          postData.blog_category = (newPost as any).blog_category || newPost.blogCategory || newPost.category || 'General';
          // Remove category field to avoid conflicts
          delete postData.category;
          break;
        case 'event':
          // Set the title
          postData.title = newPost.eventTitle || newPost.title || 'Event';

          // Set event-specific fields - ensure they're in snake_case for the database
          postData.event_type = newPost.eventType || (newPost as any).event_type || 'General';
          postData.event_theme = newPost.eventTheme || (newPost as any).event_theme || null;

          // Set sub_type to 'event' for proper filtering
          postData.sub_type = 'event';

          // Handle the event date - ensure it's a valid ISO string
          if (newPost.eventStartDatetime) {
            postData.event_start_datetime = newPost.eventStartDatetime;
          } else if ((newPost as any).event_date) {
            postData.event_start_datetime = (newPost as any).event_date;
          } else {
            postData.event_start_datetime = new Date().toISOString();
          }

          // Set the location
          postData.event_location = newPost.eventLocation || (newPost as any).event_location || null;

          // For events, store all event details in the content field as JSON
          // Check if content is already JSON
          let eventContentObj: {
            description: string;
            eventDetails: Record<string, any>;
          };

          if (typeof newPost.content === 'string') {
            try {
              const parsed = JSON.parse(newPost.content);
              eventContentObj = {
                description: parsed.description || '',
                eventDetails: parsed.eventDetails || {}
              };
            } catch (e) {
              // If not valid JSON, create a new object
              eventContentObj = {
                description: newPost.content || '',
                eventDetails: {}
              };
            }
          } else {
            eventContentObj = {
              description: '',
              eventDetails: {}
            };
          }

          // Make sure we have an eventDetails object
          if (!eventContentObj.eventDetails) {
            eventContentObj.eventDetails = {};
          }

          // Store event-specific fields in the eventDetails object
          eventContentObj.eventDetails.eventType = newPost.eventType || 'general';
          eventContentObj.eventDetails.eventTheme = newPost.eventTheme || '';
          eventContentObj.eventDetails.eventDate = postData.event_date;
          eventContentObj.eventDetails.location = postData.event_location || '';
          eventContentObj.eventDetails.registrationUrl = newPost.eventRegistrationUrl || '';

          // Convert back to JSON string
          postData.content = JSON.stringify(eventContentObj);

          // Ensure blog_category is set for filtering
          if (!postData.blog_category) {
            postData.blog_category = newPost.eventType || newPost.category || 'general';
          }

          // Ensure 'event' is in the tags for proper filtering
          if (!postData.tags) {
            postData.tags = ['event'];
          } else if (!postData.tags.includes('event')) {
            postData.tags.push('event');
          }

          // Add event type as a tag if it's not already included
          if (postData.event_type && !postData.tags.includes(postData.event_type.toLowerCase())) {
            postData.tags.push(postData.event_type.toLowerCase());
          }
          break;
        case 'marketplace':
          postData.title = newPost.title || 'Marketplace Listing';

          // For marketplace listings, we store all the details in the content field
          // Check if content is already JSON
          let contentObj: {
            description: string;
            marketplaceDetails: Record<string, any>;
          };

          if (typeof newPost.content === 'string') {
            try {
              const parsed = JSON.parse(newPost.content);
              contentObj = {
                description: parsed.description || '',
                marketplaceDetails: parsed.marketplaceDetails || {}
              };
            } catch (e) {
              // If not valid JSON, create a new object
              contentObj = {
                description: newPost.content || '',
                marketplaceDetails: {}
              };
            }
          } else {
            contentObj = {
              description: '',
              marketplaceDetails: {}
            };
          }

          // Make sure we have a marketplaceDetails object
          if (!contentObj.marketplaceDetails) {
            contentObj.marketplaceDetails = {};
          }

          // Store marketplace-specific fields in the marketplaceDetails object
          if (newPost.price !== undefined && newPost.price !== null) {
            contentObj.marketplaceDetails.price = Number(newPost.price);
          }

          if (newPost.location) {
            contentObj.marketplaceDetails.location = newPost.location;
          }

          // Add any other marketplace fields from the newPost object
          if ((newPost as any).listingType) {
            contentObj.marketplaceDetails.listingType = (newPost as any).listingType;
          }

          if ((newPost as any).priceType) {
            contentObj.marketplaceDetails.priceType = (newPost as any).priceType;
          }

          if ((newPost as any).condition) {
            contentObj.marketplaceDetails.condition = (newPost as any).condition;
          }

          if ((newPost as any).contactInfo) {
            contentObj.marketplaceDetails.contactInfo = (newPost as any).contactInfo;
          }

          // Convert back to JSON string
          postData.content = JSON.stringify(contentObj);

          // Make sure title is set
          if (!postData.title) {
            postData.title = 'Marketplace Listing';
          }

          // Set the sub_type to marketplace
          postData.sub_type = 'marketplace';
          break;
        case 'opportunity':
          postData.title = newPost.title || 'Opportunity';
          postData.opportunity_type = newPost.subType || 'General';
          postData.opportunity_deadline = newPost.deadline || null;
          postData.application_url = newPost.applicationUrl || null;
          break;
      }

      // Save to Supabase

      // Validate required fields according to database schema
      if (!postData.post_type) {
        throw new Error('Post type is required and must be one of: automated, admin, platform');
      }

      if (!postData.sub_type) {
        throw new Error('Sub type is required');
      }

      if (!postData.status) {
        throw new Error('Status is required and must be one of: draft, published, archived, pending_approval');
      }

      if (postData.post_type !== 'automated' && postData.post_type !== 'admin' && postData.post_type !== 'platform') {
        throw new Error('Post type must be one of: automated, admin, platform');
      }

      if (postData.status !== 'draft' && postData.status !== 'published' &&
          postData.status !== 'archived' && postData.status !== 'pending_approval') {
        throw new Error('Status must be one of: draft, published, archived, pending_approval');
      }

      // Log the final data being sent to the database
      console.log('Final post data being sent to Supabase:', JSON.stringify(postData, null, 2));

      // Validate required fields one more time
      if (!postData.post_type) {
        console.error('Missing required field: post_type');
      }
      if (!postData.sub_type) {
        console.error('Missing required field: sub_type');
      }
      if (!postData.status) {
        console.error('Missing required field: status');
      }
      if (!postData.content) {
        console.error('Missing field: content');
      }
      if (!postData.title) {
        console.error('Missing field: title');
      }

      // Database handles likes_count and comments_count defaults, so no need to validate them

      // Ensure media_urls is a valid JSONB object
      if (postData.media_urls && typeof postData.media_urls !== 'object') {
        console.error('Invalid data type for media_urls:', typeof postData.media_urls);
        // Fix it
        postData.media_urls = {};
      }

      // Insert the post
      let { data, error: supabaseError } = await supabase
        .from('posts')
        .insert(postData)
        .select()
        .single();

      if (supabaseError) {
        console.error('Supabase error details:', supabaseError);

        // Check for specific error types
        if (supabaseError.message && supabaseError.message.includes('violates not-null constraint')) {
          const field = supabaseError.message.match(/column "([^"]+)"/);
          if (field && field[1]) {
            throw new Error(`Required field missing: ${field[1]}`);
          }
        } else if (supabaseError.message && supabaseError.message.includes('violates check constraint')) {
          if (supabaseError.message.includes('post_type')) {
            throw new Error('Post type must be one of: automated, admin, platform');
          } else if (supabaseError.message.includes('status')) {
            throw new Error('Status must be one of: draft, published, archived, pending_approval');
          } else {
            throw new Error('One of the fields has an invalid value. Please check your inputs.');
          }
        } else if (supabaseError.message && supabaseError.message.includes('schema cache')) {
          // Handle schema cache errors
          console.error('Schema cache error:', supabaseError.message);
          throw new Error(`Database schema error: ${supabaseError.message}. Please contact support.`);
        } else if (supabaseError.message && supabaseError.message.includes('invalid input syntax')) {
          throw new Error('Invalid data format. Please check your inputs, especially numeric fields.');
        } else if (supabaseError.message && supabaseError.message.includes('duplicate key')) {
          throw new Error('A post with this ID already exists.');
        } else if (supabaseError.message && supabaseError.message.includes('invalid input value')) {
          throw new Error('Invalid input value. Please check your data format.');
        }

        // If we couldn't identify a specific error, throw the original
        throw supabaseError;
      }

      // If successful, fetch the post with author information
      const { data: postWithAuthor, error: fetchError } = await supabase
        .from('posts_with_authors')
        .select('*')
        .eq('id', data.id)
        .single();

      if (fetchError) {
        throw fetchError;
      }

      // Map to frontend model and add to local state
      console.log('Post created successfully, fetched with author:', postWithAuthor);
      const post = mapPostFromDatabase(postWithAuthor);
      console.log('Post after mapping to frontend model:', post);
      posts.value.unshift(post);

      // Track the activity
      await activityService.trackActivity('post_create', {
        post_id: post.id,
        post_type: originalPostType,
        title: post.title || '',
        content_preview: post.content?.substring(0, 100) || ''
      });

      console.log('Post created successfully:', post);
      return post;
    } catch (err: any) {
      console.error('Error creating post:', err);
      error.value = err.message || 'Failed to create post';
      return null;
    } finally {
      loading.value = false;
    }
  }

  async function commentOnPost(postId: number, comment: string) {
    try {
      // SECURITY: Sanitize comment input
      const sanitizedComment = sanitizeFormInput(comment, 2000); // 2000 char limit for comments
      if (!sanitizedComment.trim()) {
        throw new Error('Comment cannot be empty');
      }

      // Initialize services
      const globalServices = useGlobalServicesStore();
      const activityService = globalServices.activityService;
      const commentsService = useCommentsService();

      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Find the post in local state
      const post = posts.value.find(p => p.id === postId);
      if (!post) {
        throw new Error('Post not found');
      }

      // Create the comment using comments service
      await commentsService.createComment({
        post_id: postId,  // Use number directly instead of converting to string
        user_id: userId,
        content: sanitizedComment
      });

      // Database trigger automatically updates comments_count, so we just need to refresh the post
      await refreshSinglePost(postId);

      // Track the comment activity with sanitized content
      await activityService.trackActivity('post_comment', {
        post_id: postId,
        post_title: post.title || post.blogTitle || post.eventTitle || '',
        comment: sanitizedComment.substring(0, 100) + (sanitizedComment.length > 100 ? '...' : ''),
        author_id: post.userId,
        author_name: post.author || ''
      });

      return true;
    } catch (err: any) {
      console.error('Error commenting on post:', err);
      error.value = err.message || 'Failed to comment on post';
      return false;
    }
  }

  // Add comment method (alias for commentOnPost for test compatibility)
  async function addComment(postId: number, comment: string) {
    return await commentOnPost(postId, comment);
  }

  // Update comment method
  async function updateComment(commentId: number, newContent: string) {
    try {
      // SECURITY: Sanitize comment content
      const sanitizedContent = sanitizeFormInput(newContent, 2000); // 2000 char limit for comments
      if (!sanitizedContent.trim()) {
        throw new Error('Comment cannot be empty');
      }

      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Check if the comment exists and belongs to the user
      const { data: commentData, error: commentError } = await supabase
        .from('comments')
        .select('*')
        .eq('id', commentId)
        .single();

      if (commentError) throw commentError;

      if (commentData.user_id !== userId) {
        throw new Error('You do not have permission to update this comment');
      }

      // Update the comment with sanitized content
      const { error: updateError } = await supabase
        .from('comments')
        .update({
          content: sanitizedContent,
          updated_at: new Date().toISOString()
        })
        .eq('id', commentId);

      if (updateError) throw updateError;

      return true;
    } catch (err: any) {
      console.error('Error updating comment:', err);
      error.value = err.message || 'Failed to update comment';
      return false;
    }
  }

  // Delete comment method
  async function deleteComment(commentId: number) {
    try {
      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Check if the comment exists and belongs to the user
      const { data: commentData, error: commentError } = await supabase
        .from('comments')
        .select('*')
        .eq('id', commentId)
        .single();

      if (commentError) throw commentError;

      if (commentData.user_id !== userId) {
        throw new Error('You do not have permission to delete this comment');
      }

      // Delete the comment
      const { error: deleteError } = await supabase
        .from('comments')
        .delete()
        .eq('id', commentId);

      if (deleteError) throw deleteError;

      // Database trigger automatically updates comments_count, so we just need to refresh the post
      const post = posts.value.find(p => p.id === commentData.post_id);
      if (post) {
        await refreshSinglePost(commentData.post_id);
      }

      return true;
    } catch (err: any) {
      console.error('Error deleting comment:', err);
      error.value = err.message || 'Failed to delete comment';
      return false;
    }
  }

  function setFilter(filter: PostFilter) {
    currentFilter.value = { ...currentFilter.value, ...filter };
  }

  function resetFilter() {
    currentFilter.value = {
      searchQuery: '',
      postTypes: [],
      subTypes: [],
      dateRange: 'all',
      tags: []
    };
  }

  // Get a post by ID
  async function getPostById(postId: number): Promise<Post | null> {
    try {
      // First check if the post is already in our local state
      const localPost = posts.value.find(p => p.id === postId);
      if (localPost) {
        return localPost;
      }

      // If not found locally, fetch from the database
      const { data, error: err } = await supabase
        .from('posts_with_authors')
        .select('*')
        .eq('id', postId)
        .single();

      if (err) throw err;

      if (data) {
        // Map the database fields to our frontend model
        return mapPostFromDatabase(data);
      }

      return null;
    } catch (err: any) {
      console.error('Error fetching post by ID:', err);
      error.value = err.message || 'Failed to fetch post';
      return null;
    }
  }

  // Fetch posts for a specific user
  async function fetchUserPosts(options: { userId: string, postType?: string, subType?: string }) {
    loading.value = true;
    error.value = null;

    try {
      console.log('Fetching user posts with options:', options);

      // Build the query
      let query = supabase
        .from('posts_with_authors')
        .select('*')
        .eq('user_id', options.userId)
        .order('created_at', { ascending: false });

      // Handle filtering based on post type
      if (options.postType) {
        // Special handling for marketplace and event posts
        if (options.postType.toUpperCase() === 'MARKETPLACE') {
          // For marketplace posts, filter by sub_type
          query = query.eq('sub_type', 'marketplace');
        }
        else if (options.postType.toUpperCase() === 'EVENT') {
          // For event posts, filter by sub_type
          query = query.eq('sub_type', 'event');
        }
        else {
          // For other post types, use post_type
          query = query.eq('post_type', options.postType);
        }
      }

      // Add sub_type filter if provided
      if (options.subType) {
        query = query.eq('sub_type', options.subType);
      }

      // Execute the query
      const { data, error: err } = await query;

      if (err) throw err;

      console.log('User posts fetched from database:', data);

      // Map the database fields to our frontend model and store in userPosts state
      userPosts.value = data ? data.map((post: PostWithAuthor) => mapPostFromDatabase(post)) : [];

      console.log('User posts after mapping:', userPosts.value);

      return userPosts.value;
    } catch (err: any) {
      console.error('Error fetching user posts:', err);
      error.value = err.message || 'Failed to fetch user posts';
      userPosts.value = [];
      return [];
    } finally {
      loading.value = false;
    }
  }

  // Update a post - supports both signatures for test compatibility
  async function updatePost(postIdOrPost: number | string | Partial<Post>, updateData?: Partial<Post>) {
    try {
      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      let postId: number | string;
      let updatedPost: Partial<Post>;

      // Handle both signatures: updatePost(id, data) and updatePost(postObject)
      if (typeof postIdOrPost === 'object' && postIdOrPost.id !== undefined) {
        // Called as updatePost(postObject)
        postId = postIdOrPost.id;
        updatedPost = postIdOrPost;
      } else if (updateData) {
        // Called as updatePost(id, data)
        postId = postIdOrPost as number | string;
        updatedPost = { id: postId, ...updateData };
      } else {
        throw new Error('Invalid arguments for updatePost');
      }

      // Check if the post exists and belongs to the user
      const { data: postData, error: postError } = await supabase
        .from('posts')
        .select('*')
        .eq('id', postId)
        .single();

      if (postError) throw postError;

      if (postData.user_id !== userId) {
        throw new Error('Not authorized to update this post');
      }

      // Prepare the update data
      const dbUpdateData: any = {};

      // Map frontend fields to database fields
      if (updatedPost.title !== undefined) dbUpdateData.title = updatedPost.title;
      if (updatedPost.content !== undefined) dbUpdateData.content = updatedPost.content;
      if (updatedPost.subType !== undefined) dbUpdateData.sub_type = updatedPost.subType;
      if (updatedPost.category !== undefined) dbUpdateData.blog_category = updatedPost.category;
      if (updatedPost.blogCategory !== undefined) dbUpdateData.blog_category = updatedPost.blogCategory;
      if (updatedPost.location !== undefined) dbUpdateData.location = updatedPost.location;
      if (updatedPost.price !== undefined) dbUpdateData.price = updatedPost.price;
      if (updatedPost.featuredImage !== undefined) dbUpdateData.featured_image = updatedPost.featuredImage;
      if (updatedPost.tags !== undefined) dbUpdateData.tags = updatedPost.tags;

      // Add updated_at timestamp
      dbUpdateData.updated_at = new Date().toISOString();

      // Update the post
      const { error: updateError } = await supabase
        .from('posts')
        .update(dbUpdateData)
        .eq('id', postId);

      if (updateError) throw updateError;

      // Update local state if it exists
      const index = posts.value.findIndex(p => p.id === postId);
      if (index !== -1) {
        posts.value[index] = { ...posts.value[index], ...updatedPost };
      }

      // Also update in userPosts if it exists
      const userPostIndex = userPosts.value.findIndex(p => p.id === postId);
      if (userPostIndex !== -1) {
        userPosts.value[userPostIndex] = { ...userPosts.value[userPostIndex], ...updatedPost };
      }

      return true;
    } catch (err: any) {
      console.error('Error updating post:', err);
      error.value = err.message || 'Failed to update post';
      return false;
    }
  }

  // Delete a post
  async function deletePost(postId: string | number) {
    try {
      // Get the current user
      const { data: userData } = await supabase.auth.getUser();
      const userId = userData.user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Check if the post exists and belongs to the user
      const { data: postData, error: postError } = await supabase
        .from('posts')
        .select('*')
        .eq('id', postId)
        .single();

      if (postError) throw postError;

      if (postData.user_id !== userId) {
        throw new Error('Not authorized to delete this post');
      }

      // Delete the post
      const { error: deleteError } = await supabase
        .from('posts')
        .delete()
        .eq('id', postId);

      if (deleteError) throw deleteError;

      // Remove from local state if it exists
      const index = posts.value.findIndex(p => p.id === postId);
      if (index !== -1) {
        posts.value.splice(index, 1);
      }

      // Also remove from userPosts if it exists
      const userPostIndex = userPosts.value.findIndex(p => p.id === postId);
      if (userPostIndex !== -1) {
        userPosts.value.splice(userPostIndex, 1);
      }

      return true;
    } catch (err: any) {
      console.error('Error deleting post:', err);
      error.value = err.message || 'Failed to delete post';
      return false;
    }
  }

  // Type-specific getters with more inclusive filtering
  const events = computed(() => {
    return posts.value.filter(post => {
      // Check for event in postType (case-insensitive)
      const hasEventPostType = post.postType?.toUpperCase() === 'EVENT';

      // Check if the post has event-specific fields
      const hasEventFields = post.eventTitle || post.eventLocation || post.eventStartDatetime;

      // Check if the post has event in tags
      const hasEventTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase() === 'event'
      );

      // Check if the post has event in sub_type
      const hasEventSubType = post.subType?.toLowerCase() === 'event';

      return hasEventPostType || hasEventFields || hasEventTag || hasEventSubType;
    });
  });

  const articles = computed(() => {
    return posts.value.filter(post => {
      // Check for blog in postType (case-insensitive)
      const hasBlogPostType = post.postType?.toUpperCase() === 'BLOG';

      // Check if the post has blog-specific fields
      const hasBlogFields = post.blogTitle || post.blogCategory || post.blogFullContent;

      // Check if the post has blog in tags
      const hasBlogTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase() === 'blog'
      );

      // Check if the post has blog in sub_type
      const hasBlogSubType = post.subType?.toLowerCase() === 'blog';

      return hasBlogPostType || hasBlogFields || hasBlogTag || hasBlogSubType;
    });
  });

  const news = computed(() => {
    return posts.value.filter(post => {
      // Check for news in postType (case-insensitive)
      const hasNewsPostType = post.postType?.toUpperCase() === 'NEWS';

      // Check if the post has news in tags
      const hasNewsTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && tag.toLowerCase() === 'news'
      );

      // Check if the post has news in sub_type
      const hasNewsSubType = post.subType?.toLowerCase() === 'news';

      return hasNewsPostType || hasNewsTag || hasNewsSubType;
    });
  });

  const successStories = computed(() => {
    return posts.value.filter(post => {
      // Check for success_story in postType (case-insensitive)
      const hasSuccessStoryPostType = post.postType?.toUpperCase() === 'SUCCESS_STORY';

      // Check if the post has success story in tags
      const hasSuccessStoryTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && (
          tag.toLowerCase() === 'success_story' ||
          tag.toLowerCase() === 'success story'
        )
      );

      // Check if the post has success_story in sub_type
      const hasSuccessStorySubType = post.subType?.toLowerCase() === 'success_story' ||
                                    post.subType?.toLowerCase() === 'success story';

      return hasSuccessStoryPostType || hasSuccessStoryTag || hasSuccessStorySubType;
    });
  });

  // Add a marketplace getter
  const marketplace = computed(() => {
    return posts.value.filter(post => {
      // Check for marketplace in postType (case-insensitive)
      const hasMarketplacePostType = post.postType?.toUpperCase() === 'MARKETPLACE';

      // Check if the post has marketplace-specific fields
      const hasMarketplaceFields = post.price !== undefined || (post.location && post.price);

      // Check if the post has marketplace in tags
      const hasMarketplaceTag = post.tags?.some((tag: string) =>
        typeof tag === 'string' && (
          tag.toLowerCase() === 'marketplace' ||
          tag.toLowerCase() === 'market'
        )
      );

      // Check if the post has marketplace in sub_type
      const hasMarketplaceSubType = post.subType?.toLowerCase() === 'marketplace' ||
                                   post.subType?.toLowerCase() === 'market';

      return hasMarketplacePostType || hasMarketplaceFields || hasMarketplaceTag || hasMarketplaceSubType;
    });
  });

  // Type-specific fetch methods
  async function fetchEvents(filter?: Partial<PostFilter>) {
    // Use the exact database values
    return await fetchPosts({
      ...filter,
      // We're filtering by sub_type in the component, so no need to filter here
    });
  }

  async function fetchArticles(filter?: Partial<PostFilter>) {
    return await fetchPosts({
      ...filter,
      // Filter by sub_type for blog posts
      subTypes: ['blog', 'blog_article']
    });
  }

  async function fetchNews(filter?: Partial<PostFilter>) {
    return await fetchPosts({
      ...filter,
      // Filter by sub_type for news
      subTypes: ['news']
    });
  }

  async function fetchSuccessStories(filter?: Partial<PostFilter>) {
    return await fetchPosts({
      ...filter,
      // Filter by sub_type for success stories
      subTypes: ['success_story']
    });
  }

  // Add a marketplace fetch method
  async function fetchMarketplace(filter?: Partial<PostFilter>) {
    return await fetchPosts({
      ...filter,
      // We're filtering by sub_type in the component, so no need to filter here
    });
  }

  // Get a post by slug
  async function getPostBySlug(slug: string, postType?: string): Promise<Post | null> {
    try {
      // First check if the post is already in our local state
      const localPost = posts.value.find(p => p.slug === slug && (!postType || p.postType === postType));
      if (localPost) {
        return localPost;
      }

      // If not found locally, fetch from the database
      let query = supabase
        .from('posts_with_authors')
        .select('*')
        .eq('slug', slug);

      if (postType) {
        query = query.eq('post_type', postType);
      }

      const { data, error: err } = await query.single();

      if (err) throw err;

      if (data) {
        // Map the database fields to our frontend model
        return mapPostFromDatabase(data);
      }

      return null;
    } catch (err: any) {
      console.error('Error fetching post by slug:', err);
      error.value = err.message || 'Failed to fetch post';
      return null;
    }
  }

  return {
    // State
    posts,
    userPosts,
    loading,
    error,
    currentFilter,
    nextCursor,
    hasMorePosts,

    // Getters
    filteredPosts,
    events,
    articles,
    news,
    successStories,
    marketplace,

    // Actions
    fetchPosts,
    fetchMorePosts,
    fetchUserPosts,
    likePost,
    createPost,
    commentOnPost,
    addComment,
    updateComment,
    deleteComment,
    setFilter,
    resetFilter,
    getPostById,
    getPostBySlug,
    updatePost,
    deletePost,

    // Type-specific fetch methods
    fetchEvents,
    fetchArticles,
    fetchNews,
    fetchSuccessStories,
    fetchMarketplace
  };
});
